#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)


class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flashed = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()

    def _reset_organs_features(self):
        """重置所有organs相关特征"""
        # 基础状态：15维organs状态列表 (宝箱1-13, buff, end)
        self.organs_states = np.zeros(15, dtype=np.float32)

        # 详细特征：每个organ的核心信息
        # 对于每个可能的organ (最多15个)，存储：[status, distance, direction_x, direction_y, priority]
        self.organs_detailed_features = np.zeros((15, 5), dtype=np.float32)

        # 简化的空间特征：5维基础空间信息
        self.organs_spatial_features = np.zeros(5, dtype=np.float32)

        # 简化的优先级特征：1维游戏进度
        self.organs_priority_features = np.zeros(1, dtype=np.float32)

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = min(1.0, current_value + 0.1)

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 解析map_info，暂未使用
        
        # 闪现是否可用 -- 新增一个字段来代表闪现是否可用，默认初始化的时候可以为True
        if hero['talent']['status'] == 0:
            self.is_flashed = False

        # 高效生成各类flag矩阵
        map_array = np.array([[v for v in row['values']] for row in map_info], dtype=np.float32)
        self.treasure_flag = (map_array == 4).astype(np.float32).flatten()
        self.end_flag = (map_array == 3).astype(np.float32).flatten()
        self.obstacle_flag = (map_array == 0).astype(np.float32).flatten()
        self.buff_flag = (map_array == 6).astype(np.float32).flatten()

        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 更新记忆矩阵
        self.memory_update(self.cur_pos)
        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs特征 - 高效重置
        self.organs_states.fill(0)
        self.organs_detailed_features.fill(0)
        self.organs_spatial_features.fill(0)
        self.organs_priority_features.fill(0)

        # 收集所有organs信息用于高级特征计算
        visible_treasures = []
        visible_buffs = []
        end_organ = None

        # 遍历organs数据进行多层次特征提取
        for organ in obs["frame_state"]["organs"]:
            config_id = organ["config_id"]
            sub_type = organ["sub_type"]
            status = organ["status"]

            # 基础状态特征
            if status == 1:
                if sub_type == 1 or sub_type == 2:  # treasure and buff
                    self.organs_states[config_id] = 1
                elif sub_type == 4:  # end
                    self.organs_states[14] = 1
            else:
                if config_id < 15:
                    self.organs_states[config_id] = 0

            # 详细特征提取（仅对可见的organs）
            if status != -1:  # 视野内的organ
                # 安全的索引计算
                organ_idx = min(config_id, 14) if config_id < 15 else 14  # end用索引14，防止越界

                # 计算相对位置和距离
                if status == 1 and "pos" in organ:
                    organ_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    relative_pos = (organ_pos[0] - self.cur_pos[0], organ_pos[1] - self.cur_pos[1])
                    distance = np.linalg.norm(relative_pos)

                    # 归一化方向向量
                    if distance > 1e-4:
                        direction_x = np.clip(relative_pos[0] / distance, -1.0, 1.0)
                        direction_y = np.clip(relative_pos[1] / distance, -1.0, 1.0)
                    else:
                        direction_x = direction_y = 0

                    # 归一化距离 (0-1范围)
                    distance_norm = min(distance / (128 * 1.41), 1.0)

                    # 简化的优先级计算
                    priority = 1.0 / (1.0 + distance / 20.0)  # 基于距离的简单优先级
                    if sub_type == 4:  # 终点
                        priority *= 2.0
                    elif sub_type == 2:  # buff
                        priority *= 1.5

                    # 存储详细特征 - 确保索引安全 (移除cooldown维度)
                    if 0 <= organ_idx < 15:
                        self.organs_detailed_features[organ_idx] = [
                            float(status),      # 状态 (0/1)
                            distance_norm,      # 归一化距离
                            direction_x,        # X方向
                            direction_y,        # Y方向
                            priority           # 优先级分数
                        ]

                    # 收集用于空间特征计算
                    if sub_type == 1:  # treasure
                        visible_treasures.append((distance, priority, organ))
                    elif sub_type == 2:  # buff
                        visible_buffs.append((distance, priority, organ))
                    elif sub_type == 4:  # end
                        end_organ = (distance, priority, organ)
                    except (KeyError, TypeError, ValueError) as e:
                        # 处理数据异常，继续处理其他organs
                        continue

        # 简化的空间和优先级特征计算
        # 最近的宝箱和buff信息
        if visible_treasures:
            closest_treasure = min(visible_treasures, key=lambda x: x[0])
            self.organs_spatial_features[0] = min(closest_treasure[0] / 100.0, 1.0)  # 最近宝箱距离
            self.organs_spatial_features[1] = len(visible_treasures) / 10.0  # 可见宝箱数量

        if visible_buffs:
            closest_buff = min(visible_buffs, key=lambda x: x[0])
            self.organs_spatial_features[2] = min(closest_buff[0] / 100.0, 1.0)  # 最近buff距离
            self.organs_spatial_features[3] = len(visible_buffs) / 3.0  # 可见buff数量

        if end_organ:
            self.organs_spatial_features[4] = min(end_organ[0] / 100.0, 1.0)  # 终点距离

        # 游戏进度
        self.organs_priority_features[0] = min(self.step_no / 1000.0, 1.0)




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        # End position
        # 终点位置
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 4:
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                # if end_pos is not found, use relative position to predict end_pos
                # 如果终点位置未找到，使用相对位置预测终点位置
                elif (not self.is_end_pos_found) and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or self.end_pos_dir != end_pos_dir
                    or self.end_pos_dis != end_pos_dis
                ):
                    distance = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))

                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + delta_x))),
                        max(0, min(128, round(self.cur_pos[1] + delta_z))),
                    )

                    self.end_pos_dir = end_pos_dir
                    self.end_pos_dis = end_pos_dis

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        # 当前位置的one-hot编码特征
        # 目标位置的one-hot编码
        pos_row = [0] * 128
        pos_row[self.cur_pos[0]] = 1
        pos_col = [0] * 128
        pos_col[self.cur_pos[1]] = 1
        self.cur_pos_onehot = np.array(pos_row + pos_col, dtype=np.float32)

        self.move_usable = True
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)

        # Legal action
        # 合法动作
        legal_action = self.get_legal_action()

        # Feature
        # 特征 - 集成简化的organs特征
        organs_features = np.concatenate([
            self.organs_states,                    # 15维：基础状态
            self.organs_detailed_features.flatten(),  # 75维：详细特征 (15*5)
            self.organs_spatial_features,          # 5维：空间特征
            self.organs_priority_features          # 1维：游戏进度
        ])  # 总计96维organs特征

        feature = np.concatenate([
            self.cur_pos_norm,           # 2维：当前位置
            self.feature_end_pos,        # 6维：终点特征
            self.feature_history_pos,    # 6维：历史位置特征
            organs_features,             # 96维：organs特征系统
            self.cur_pos_onehot,        # 256维：位置one-hot
            legal_action                # 16维：合法动作
        ])  # 总计382维特征

        return (
            feature,
            legal_action,
            reward_process(self.feature_end_pos[-1], self.feature_history_pos[-1]),
        )

    def get_legal_action(self):
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中
        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            self.bad_move_ids = set()

        # 前8个为移动动作，后8个为闪现动作
        legal_action = [self.move_usable] * 8
        # 闪现动作的合法性由self.is_flashed控制
        if self.is_flashed:
            legal_action += [True] * 8
        else:
            legal_action += [False] * 8

        # bad_move_ids只影响前8个移动动作
        for move_id in self.bad_move_ids:
            if 0 <= move_id < 8:
                legal_action[move_id] = False

        # 如果前8个移动动作都不可用，则重置bad_move_ids，前8个全部可用，后8个不变
        if not any(legal_action[:8]):
            self.bad_move_ids = set()
            for i in range(8):
                legal_action[i] = self.move_usable

        return legal_action
