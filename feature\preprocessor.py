#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)


class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flashed = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()

    def _reset_organs_features(self):
        """重置organs原始特征"""
        # 原始organs特征矩阵：每个organ的完整原始信息
        # 最多15个organs (宝箱1-13, buff, end)，每个organ 8维原始特征
        # [sub_type, config_id, status, pos_x, pos_z, cooldown, relative_distance, relative_direction]
        self.organs_raw_features = np.zeros((15, 8), dtype=np.float32)

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = min(1.0, current_value + 0.1)

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 解析map_info，暂未使用
        
        # 闪现是否可用 -- 新增一个字段来代表闪现是否可用，默认初始化的时候可以为True
        if hero['talent']['status'] == 0:
            self.is_flashed = False

        # 高效生成各类flag矩阵
        map_array = np.array([[v for v in row['values']] for row in map_info], dtype=np.float32)
        self.treasure_flag = (map_array == 4).astype(np.float32).flatten()
        self.end_flag = (map_array == 3).astype(np.float32).flatten()
        self.obstacle_flag = (map_array == 0).astype(np.float32).flatten()
        self.buff_flag = (map_array == 6).astype(np.float32).flatten()

        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 更新记忆矩阵
        self.memory_update(self.cur_pos)
        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs原始特征矩阵
        self.organs_raw_features.fill(-1)  # 用-1表示无效/未知状态

        # 遍历organs数据，直接存储原始信息
        for organ in obs["frame_state"]["organs"]:
            config_id = organ.get("config_id", -1)
            sub_type = organ.get("sub_type", -1)
            status = organ.get("status", -1)

            # 确定存储索引
            if sub_type == 4:  # 终点
                organ_idx = 14
            elif 0 <= config_id < 14:  # 宝箱和buff
                organ_idx = config_id
            else:
                continue  # 跳过无效的organ

            # 存储原始特征，让神经网络自己学习如何使用
            self.organs_raw_features[organ_idx] = [
                float(sub_type),                                    # 类型 (1=treasure, 2=buff, 4=end)
                float(config_id),                                   # 配置ID
                float(status),                                      # 状态 (-1=视野外, 0=冷却中, 1=可获取)
                float(organ.get("pos", {}).get("x", -1)),          # 绝对位置X (如果未知则为-1)
                float(organ.get("pos", {}).get("z", -1)),          # 绝对位置Z (如果未知则为-1)
                float(organ.get("cooldown", 0)),                   # 冷却时间 (原始值)
                float(RelativeDistance.get(organ.get("relative_pos", {}).get("l2_distance", "RELATIVE_DISTANCE_NONE"), 0)),  # 相对距离等级
                float(RelativeDirection.get(organ.get("relative_pos", {}).get("direction", "North"), 0))  # 相对方向
            ]




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        # End position
        # 终点位置
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 4:
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                # if end_pos is not found, use relative position to predict end_pos
                # 如果终点位置未找到，使用相对位置预测终点位置
                elif (not self.is_end_pos_found) and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or self.end_pos_dir != end_pos_dir
                    or self.end_pos_dis != end_pos_dis
                ):
                    distance = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))

                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + delta_x))),
                        max(0, min(128, round(self.cur_pos[1] + delta_z))),
                    )

                    self.end_pos_dir = end_pos_dir
                    self.end_pos_dis = end_pos_dis

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        # 当前位置的one-hot编码特征
        # 目标位置的one-hot编码
        pos_row = [0] * 128
        pos_row[self.cur_pos[0]] = 1
        pos_col = [0] * 128
        pos_col[self.cur_pos[1]] = 1
        self.cur_pos_onehot = np.array(pos_row + pos_col, dtype=np.float32)

        self.move_usable = True
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)

        # Legal action
        # 合法动作
        legal_action = self.get_legal_action()

        # Feature
        # 特征 - 使用原始organs特征，移除冗余的终点特征
        feature = np.concatenate([
            self.cur_pos_norm,                     # 2维：当前位置
            self.feature_history_pos,              # 6维：历史位置特征
            self.organs_raw_features.flatten(),    # 120维：原始organs特征 (15*8)
            self.cur_pos_onehot,                  # 256维：位置one-hot
            legal_action                          # 16维：合法动作
        ])  # 总计400维特征

        return (
            feature,
            legal_action,
            reward_process(self.feature_end_pos[-1], self.feature_history_pos[-1]),
        )

    def get_legal_action(self):
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中
        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            self.bad_move_ids = set()

        # 前8个为移动动作，后8个为闪现动作
        legal_action = [self.move_usable] * 8
        # 闪现动作的合法性由self.is_flashed控制
        if self.is_flashed:
            legal_action += [True] * 8
        else:
            legal_action += [False] * 8

        # bad_move_ids只影响前8个移动动作
        for move_id in self.bad_move_ids:
            if 0 <= move_id < 8:
                legal_action[move_id] = False

        # 如果前8个移动动作都不可用，则重置bad_move_ids，前8个全部可用，后8个不变
        if not any(legal_action[:8]):
            self.bad_move_ids = set()
            for i in range(8):
                legal_action[i] = self.move_usable

        return legal_action
