#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)


class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flashed = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()

    def _reset_organs_features(self):
        """重置所有organs相关特征"""
        # 基础状态：15维organs状态列表 (宝箱1-13, buff, end)
        self.organs_states = np.zeros(15, dtype=np.float32)

        # 高级特征：每个organ的详细信息
        # 对于每个可能的organ (最多15个)，存储：[status, distance, direction_x, direction_y, cooldown_norm, priority]
        self.organs_detailed_features = np.zeros((15, 6), dtype=np.float32)

        # 空间感知特征：基于距离的分层表示
        self.organs_spatial_features = np.zeros(20, dtype=np.float32)  # 4个距离层次 * 5个特征维度

        # 动态优先级特征：基于游戏状态的智能优先级
        self.organs_priority_features = np.zeros(8, dtype=np.float32)

        # 历史交互特征：记录与organs的交互历史
        self.organs_interaction_history = np.zeros(10, dtype=np.float32)

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = min(1.0, current_value + 0.1)

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 解析map_info，暂未使用
        
        # 闪现是否可用 -- 新增一个字段来代表闪现是否可用，默认初始化的时候可以为True
        if hero['talent']['status'] == 0:
            self.is_flashed = False

        # 高效生成各类flag矩阵
        map_array = np.array([[v for v in row['values']] for row in map_info], dtype=np.float32)
        self.treasure_flag = (map_array == 4).astype(np.float32).flatten()
        self.end_flag = (map_array == 3).astype(np.float32).flatten()
        self.obstacle_flag = (map_array == 0).astype(np.float32).flatten()
        self.buff_flag = (map_array == 6).astype(np.float32).flatten()

        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 更新记忆矩阵
        self.memory_update(self.cur_pos)
        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs特征 - 高效重置
        self.organs_states.fill(0)
        self.organs_detailed_features.fill(0)
        self.organs_spatial_features.fill(0)
        self.organs_priority_features.fill(0)
        # 注意：organs_interaction_history不重置，保持历史记忆

        # 收集所有organs信息用于高级特征计算
        visible_treasures = []
        visible_buffs = []
        end_organ = None

        # 遍历organs数据进行多层次特征提取
        for organ in obs["frame_state"]["organs"]:
            config_id = organ["config_id"]
            sub_type = organ["sub_type"]
            status = organ["status"]

            # 基础状态特征
            if status == 1:
                if sub_type == 1 or sub_type == 2:  # treasure and buff
                    self.organs_states[config_id] = 1
                elif sub_type == 4:  # end
                    self.organs_states[14] = 1
            else:
                if config_id < 15:
                    self.organs_states[config_id] = 0

            # 详细特征提取（仅对可见的organs）
            if status != -1:  # 视野内的organ
                # 安全的索引计算
                organ_idx = min(config_id, 14) if config_id < 15 else 14  # end用索引14，防止越界

                # 计算相对位置和距离
                if status == 1 and "pos" in organ:
                    try:
                        organ_pos = (organ["pos"]["x"], organ["pos"]["z"])
                        relative_pos = (organ_pos[0] - self.cur_pos[0], organ_pos[1] - self.cur_pos[1])
                        distance = np.linalg.norm(relative_pos)

                        # 归一化方向向量
                        if distance > 1e-4:
                            direction_x = np.clip(relative_pos[0] / distance, -1.0, 1.0)
                            direction_y = np.clip(relative_pos[1] / distance, -1.0, 1.0)
                        else:
                            direction_x = direction_y = 0

                        # 归一化距离 (0-1范围)
                        distance_norm = min(distance / (128 * 1.41), 1.0)

                        # 归一化冷却时间
                        cooldown = max(0, organ.get("cooldown", 0))  # 确保非负
                        cooldown_norm = min(cooldown / 100.0, 1.0)  # 限制在[0,1]范围

                        # 计算优先级分数
                        priority = self._calculate_organ_priority(organ, distance, sub_type)

                        # 存储详细特征 - 确保索引安全
                        if 0 <= organ_idx < 15:
                            self.organs_detailed_features[organ_idx] = [
                                float(status),      # 状态 (0/1)
                                distance_norm,      # 归一化距离
                                direction_x,        # X方向
                                direction_y,        # Y方向
                                cooldown_norm,      # 归一化冷却时间
                                priority           # 优先级分数
                            ]

                        # 收集用于空间特征计算
                        if sub_type == 1:  # treasure
                            visible_treasures.append((distance, priority, organ))
                        elif sub_type == 2:  # buff
                            visible_buffs.append((distance, priority, organ))
                        elif sub_type == 4:  # end
                            end_organ = (distance, priority, organ)
                    except (KeyError, TypeError, ValueError) as e:
                        # 处理数据异常，继续处理其他organs
                        continue

        # 计算空间感知特征
        self._compute_spatial_features(visible_treasures, visible_buffs, end_organ)

        # 计算动态优先级特征
        self._compute_priority_features(visible_treasures, visible_buffs, end_organ)

        # 更新交互历史特征
        self._update_interaction_history(visible_treasures, visible_buffs)




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        # End position
        # 终点位置
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 4:
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                # if end_pos is not found, use relative position to predict end_pos
                # 如果终点位置未找到，使用相对位置预测终点位置
                elif (not self.is_end_pos_found) and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or self.end_pos_dir != end_pos_dir
                    or self.end_pos_dis != end_pos_dis
                ):
                    distance = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))

                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + delta_x))),
                        max(0, min(128, round(self.cur_pos[1] + delta_z))),
                    )

                    self.end_pos_dir = end_pos_dir
                    self.end_pos_dis = end_pos_dis

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        # 当前位置的one-hot编码特征
        # 目标位置的one-hot编码
        pos_row = [0] * 128
        pos_row[self.cur_pos[0]] = 1
        pos_col = [0] * 128
        pos_col[self.cur_pos[1]] = 1
        self.cur_pos_onehot = np.array(pos_row + pos_col, dtype=np.float32)

        self.move_usable = True
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)

        # Legal action
        # 合法动作
        legal_action = self.get_legal_action()

        # Feature
        # 特征 - 集成多层次organs特征
        organs_features = np.concatenate([
            self.organs_states,                    # 15维：基础状态
            self.organs_detailed_features.flatten(),  # 90维：详细特征 (15*6)
            self.organs_spatial_features,          # 20维：空间感知特征
            self.organs_priority_features,         # 8维：动态优先级特征
            self.organs_interaction_history        # 10维：交互历史特征
        ])  # 总计143维organs特征

        feature = np.concatenate([
            self.cur_pos_norm,           # 2维：当前位置
            self.feature_end_pos,        # 6维：终点特征
            self.feature_history_pos,    # 6维：历史位置特征
            organs_features,             # 143维：完整organs特征系统
            self.cur_pos_onehot,        # 256维：位置one-hot
            legal_action                # 16维：合法动作
        ])  # 总计429维特征

        return (
            feature,
            legal_action,
            reward_process(self.feature_end_pos[-1], self.feature_history_pos[-1]),
        )

    def get_legal_action(self):
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中
        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            self.bad_move_ids = set()

        # 前8个为移动动作，后8个为闪现动作
        legal_action = [self.move_usable] * 8
        # 闪现动作的合法性由self.is_flashed控制
        if self.is_flashed:
            legal_action += [True] * 8
        else:
            legal_action += [False] * 8

        # bad_move_ids只影响前8个移动动作
        for move_id in self.bad_move_ids:
            if 0 <= move_id < 8:
                legal_action[move_id] = False

        # 如果前8个移动动作都不可用，则重置bad_move_ids，前8个全部可用，后8个不变
        if not any(legal_action[:8]):
            self.bad_move_ids = set()
            for i in range(8):
                legal_action[i] = self.move_usable

        return legal_action

    def _calculate_organ_priority(self, organ, distance, sub_type):
        """计算organ的动态优先级分数"""
        priority = 0.0

        # 基础优先级：距离越近优先级越高
        if distance > 0:
            priority += 1.0 / (1.0 + distance / 10.0)

        # 类型优先级
        if sub_type == 4:  # 终点最高优先级
            priority += 2.0
        elif sub_type == 2:  # buff次高优先级
            priority += 1.5
        elif sub_type == 1:  # treasure基础优先级
            priority += 1.0

        # 状态调整
        if organ["status"] == 1:  # 可获取状态
            priority *= 1.5
        elif organ["status"] == 0:  # 冷却状态
            priority *= 0.3

        # 冷却时间调整
        cooldown = organ.get("cooldown", 0)
        if cooldown > 0:
            priority *= (1.0 - min(cooldown / 100.0, 0.8))  # 冷却时间越长优先级越低

        # 步数调整：游戏后期终点优先级更高
        if sub_type == 4 and self.step_no > 500:
            priority *= 1.5

        return min(priority, 5.0)  # 限制最大优先级

    def _compute_spatial_features(self, visible_treasures, visible_buffs, end_organ):
        """计算基于距离分层的空间感知特征"""
        # 定义4个距离层次：很近(0-20), 近(20-50), 中(50-100), 远(100+)
        distance_thresholds = [20, 50, 100, float('inf')]

        for layer_idx, threshold in enumerate(distance_thresholds):
            base_idx = layer_idx * 5

            # 统计每个距离层次的organs
            treasure_count = sum(1 for d, p, o in visible_treasures if d <= threshold and (layer_idx == 0 or d > distance_thresholds[layer_idx-1]))
            buff_count = sum(1 for d, p, o in visible_buffs if d <= threshold and (layer_idx == 0 or d > distance_thresholds[layer_idx-1]))

            # 计算该层次的平均优先级
            layer_treasures = [p for d, p, o in visible_treasures if d <= threshold and (layer_idx == 0 or d > distance_thresholds[layer_idx-1])]
            layer_buffs = [p for d, p, o in visible_buffs if d <= threshold and (layer_idx == 0 or d > distance_thresholds[layer_idx-1])]

            avg_treasure_priority = np.mean(layer_treasures) if layer_treasures else 0
            avg_buff_priority = np.mean(layer_buffs) if layer_buffs else 0

            # 计算该层次的密度特征
            total_count = treasure_count + buff_count
            density = min(total_count / 5.0, 1.0)  # 归一化密度

            # 存储空间特征
            self.organs_spatial_features[base_idx:base_idx+5] = [
                treasure_count / 5.0,  # 归一化treasure数量
                buff_count / 2.0,      # 归一化buff数量
                avg_treasure_priority / 5.0,  # 归一化平均treasure优先级
                avg_buff_priority / 5.0,       # 归一化平均buff优先级
                density                        # 该层次的organ密度
            ]

    def _compute_priority_features(self, visible_treasures, visible_buffs, end_organ):
        """计算动态优先级特征"""
        # 最高优先级treasure
        if visible_treasures:
            best_treasure = max(visible_treasures, key=lambda x: x[1])
            self.organs_priority_features[0] = best_treasure[1] / 5.0  # 归一化优先级
            self.organs_priority_features[1] = min(best_treasure[0] / 100.0, 1.0)  # 归一化距离

        # 最高优先级buff
        if visible_buffs:
            best_buff = max(visible_buffs, key=lambda x: x[1])
            self.organs_priority_features[2] = best_buff[1] / 5.0
            self.organs_priority_features[3] = min(best_buff[0] / 100.0, 1.0)

        # 终点信息
        if end_organ:
            self.organs_priority_features[4] = end_organ[1] / 5.0
            self.organs_priority_features[5] = min(end_organ[0] / 100.0, 1.0)

        # 全局策略特征
        total_visible = len(visible_treasures) + len(visible_buffs)
        self.organs_priority_features[6] = min(total_visible / 10.0, 1.0)  # 可见organ总数

        # 游戏阶段特征：早期收集，后期冲刺
        game_progress = min(self.step_no / 1000.0, 1.0)
        self.organs_priority_features[7] = game_progress

    def _update_interaction_history(self, visible_treasures, visible_buffs):
        """更新与organs的交互历史特征"""
        # 简化的历史特征：记录最近的交互模式
        # 这里可以根据需要扩展更复杂的历史记忆机制

        # 当前可见的高价值目标数量
        high_value_treasures = sum(1 for d, p, o in visible_treasures if p > 2.0)
        high_value_buffs = sum(1 for d, p, o in visible_buffs if p > 2.0)

        # 更新历史特征（使用滑动平均）
        alpha = 0.1  # 学习率
        self.organs_interaction_history[0] = (1-alpha) * self.organs_interaction_history[0] + alpha * (high_value_treasures / 5.0)
        self.organs_interaction_history[1] = (1-alpha) * self.organs_interaction_history[1] + alpha * (high_value_buffs / 2.0)

        # 记录最近的移动模式（是否在接近高价值目标）
        if visible_treasures or visible_buffs:
            closest_high_value = None
            min_distance = float('inf')

            for d, p, o in visible_treasures + visible_buffs:
                if p > 2.0 and d < min_distance:
                    min_distance = d
                    closest_high_value = (d, p)

            if closest_high_value:
                # 记录接近高价值目标的趋势
                approach_trend = max(0, 1.0 - min_distance / 50.0)  # 距离越近趋势越强
                self.organs_interaction_history[2] = (1-alpha) * self.organs_interaction_history[2] + alpha * approach_trend

        # 其他历史特征可以根据需要添加
        # 例如：收集效率、路径优化历史等
